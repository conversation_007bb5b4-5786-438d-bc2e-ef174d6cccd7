#!/usr/bin/env python3
"""
My FastMCP Server for Augment Code
A custom MCP server with useful tools
"""

from fastmcp import FastMCP
import os
import subprocess
import json
from datetime import datetime

# Create a FastMCP server instance
mcp = FastMCP("My Custom Tools 🛠️")

@mcp.tool
def get_system_info() -> dict:
    """Get basic system information."""
    try:
        result = subprocess.run(['uname', '-a'], capture_output=True, text=True)
        system_info = result.stdout.strip()
        
        # Get current directory
        current_dir = os.getcwd()
        
        # Get current time
        current_time = datetime.now().isoformat()
        
        return {
            "system": system_info,
            "current_directory": current_dir,
            "current_time": current_time,
            "python_version": subprocess.run(['python3', '--version'], capture_output=True, text=True).stdout.strip()
        }
    except Exception as e:
        return {"error": str(e)}

@mcp.tool
def list_directory(path: str = ".") -> dict:
    """List contents of a directory."""
    try:
        items = []
        for item in os.listdir(path):
            item_path = os.path.join(path, item)
            is_dir = os.path.isdir(item_path)
            size = os.path.getsize(item_path) if not is_dir else None
            items.append({
                "name": item,
                "type": "directory" if is_dir else "file",
                "size": size
            })
        
        return {
            "path": os.path.abspath(path),
            "items": items,
            "total_items": len(items)
        }
    except Exception as e:
        return {"error": str(e)}

@mcp.tool
def run_command(command: str, working_dir: str = ".") -> dict:
    """Run a shell command safely (read-only commands recommended)."""
    try:
        # Basic safety check - only allow certain safe commands
        safe_commands = ['ls', 'pwd', 'whoami', 'date', 'echo', 'cat', 'head', 'tail', 'grep', 'find', 'which', 'python3', 'pip3', 'git']
        
        cmd_parts = command.split()
        if not cmd_parts or cmd_parts[0] not in safe_commands:
            return {"error": f"Command '{cmd_parts[0] if cmd_parts else 'empty'}' not allowed. Safe commands: {', '.join(safe_commands)}"}
        
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            cwd=working_dir,
            timeout=30  # 30 second timeout
        )
        
        return {
            "command": command,
            "working_directory": working_dir,
            "return_code": result.returncode,
            "stdout": result.stdout,
            "stderr": result.stderr
        }
    except subprocess.TimeoutExpired:
        return {"error": "Command timed out after 30 seconds"}
    except Exception as e:
        return {"error": str(e)}

@mcp.resource("config://server-info")
def get_server_info():
    """Get information about this MCP server."""
    return {
        "name": "My Custom Tools",
        "version": "1.0.0",
        "description": "A custom FastMCP server with system utilities",
        "tools": ["get_system_info", "list_directory", "run_command"],
        "author": "Chris",
        "created": datetime.now().isoformat()
    }

@mcp.prompt
def analyze_directory(path: str = ".") -> str:
    """Generate a prompt to analyze a directory structure."""
    return f"""Please analyze the directory structure at: {path}

Look for:
1. Project type (based on files like package.json, requirements.txt, etc.)
2. Main programming languages used
3. Project structure and organization
4. Any configuration files
5. Documentation files

Provide insights about what this project might be and how it's organized."""

if __name__ == "__main__":
    print("🚀 Starting My Custom FastMCP Server...")
    print("Available tools:")
    print("  - get_system_info: Get system information")
    print("  - list_directory: List directory contents")
    print("  - run_command: Run safe shell commands")
    print("  - Resource: config://server-info")
    print("  - Prompt: analyze_directory")
    print("\nServer ready for MCP connections...")
    mcp.run()
