#!/usr/bin/env python3
"""
Demo FastMCP Server
A simple example showing FastMCP capabilities
"""

from fastmcp import Fast<PERSON><PERSON>

# Create a FastMCP server instance
mcp = FastMCP("Demo Server 🚀")

@mcp.tool
def add(a: int, b: int) -> int:
    """Add two numbers together."""
    return a + b

@mcp.tool
def multiply(a: float, b: float) -> float:
    """Multiply two numbers together."""
    return a * b

@mcp.tool
def greet(name: str) -> str:
    """Generate a friendly greeting."""
    return f"Hello, {name}! Welcome to FastMCP! 🎉"

@mcp.resource("config://version")
def get_version():
    """Get the server version."""
    return "Demo Server v1.0.0"

@mcp.resource("config://info")
def get_info():
    """Get server information."""
    return {
        "name": "Demo Server",
        "description": "A simple FastMCP demonstration server",
        "tools": ["add", "multiply", "greet"],
        "resources": ["config://version", "config://info"]
    }

@mcp.prompt
def summarize_request(text: str) -> str:
    """Generate a prompt asking for a summary."""
    return f"Please summarize the following text:\n\n{text}"

if __name__ == "__main__":
    print("Starting FastMCP Demo Server...")
    print("This server provides:")
    print("- Tools: add, multiply, greet")
    print("- Resources: config://version, config://info")
    print("- Prompts: summarize_request")
    print("\nRunning on STDIO transport...")
    mcp.run()
