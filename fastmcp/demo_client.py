#!/usr/bin/env python3
"""
Demo FastMCP Client
A simple example showing how to connect to and use a FastMCP server
"""

import asyncio
from fastmcp import Client

async def main():
    """Demonstrate FastMCP client capabilities."""
    print("FastMCP Client Demo")
    print("==================")
    
    # Connect to the demo server using in-memory transport
    # In a real scenario, you might connect via stdio or HTTP
    print("\n1. Connecting to demo server...")
    
    # For this demo, we'll import and use the server directly
    from demo_server import mcp as demo_server
    
    async with Client(demo_server) as client:
        print("✅ Connected to Demo Server!")
        
        # List available tools
        print("\n2. Listing available tools...")
        tools = await client.list_tools()
        print(f"Available tools: {[tool.name for tool in tools.tools]}")
        
        # List available resources
        print("\n3. Listing available resources...")
        resources = await client.list_resources()
        print(f"Available resources: {[resource.uri for resource in resources.resources]}")
        
        # List available prompts
        print("\n4. Listing available prompts...")
        prompts = await client.list_prompts()
        print(f"Available prompts: {[prompt.name for prompt in prompts.prompts]}")
        
        # Call some tools
        print("\n5. Calling tools...")
        
        # Test the add tool
        result = await client.call_tool("add", {"a": 5, "b": 3})
        print(f"add(5, 3) = {result.content[0].text}")
        
        # Test the multiply tool
        result = await client.call_tool("multiply", {"a": 4.5, "b": 2.0})
        print(f"multiply(4.5, 2.0) = {result.content[0].text}")
        
        # Test the greet tool
        result = await client.call_tool("greet", {"name": "FastMCP User"})
        print(f"greet('FastMCP User') = {result.content[0].text}")
        
        # Read some resources
        print("\n6. Reading resources...")
        
        # Read version resource
        version = await client.read_resource("config://version")
        print(f"Server version: {version.contents[0].text}")
        
        # Read info resource
        info = await client.read_resource("config://info")
        print(f"Server info: {info.contents[0].text}")
        
        # Use a prompt
        print("\n7. Using prompts...")
        prompt_result = await client.get_prompt("summarize_request", {"text": "FastMCP is awesome!"})
        print(f"Generated prompt: {prompt_result.messages[0].content.text}")
        
        print("\n✅ Demo completed successfully!")

if __name__ == "__main__":
    asyncio.run(main())
