# Memory MCP Server Setup Guide

## Overview
This Memory MCP Server allows <PERSON> to remember:
- **Login Credentials** (encrypted) - usernames, passwords, API keys
- **Processes** - step-by-step procedures for tasks like "looking for parts"
- **General Memories** - any information you want <PERSON> to remember

## Installation

### 1. Install Dependencies
```bash
cd /home/<USER>/projects/fastmcp
source fastmcp_env/bin/activate
pip install cryptography
```

### 2. Set Up Claude Desktop Configuration

The configuration file needs to be placed in the correct location for your system:

**Linux/Mac:**
```bash
# Create the config directory if it doesn't exist
mkdir -p ~/.config/claude-desktop

# Copy the configuration
cp claude_desktop_config.json ~/.config/claude-desktop/claude_desktop_config.json
```

**Windows:**
```cmd
# Create the config directory
mkdir "%APPDATA%\Claude\claude_desktop_config.json"

# Copy the configuration file there
```

### 3. Update Paths in Configuration

Edit the configuration file to use the correct absolute paths:
```json
{
  "mcpServers": {
    "memory-server": {
      "command": "python3",
      "args": ["/home/<USER>/projects/memory_mcp_server.py"],
      "env": {
        "PYTHONPATH": "/home/<USER>/projects/fastmcp/src"
      }
    }
  }
}
```

### 4. Test the Server

Test the memory server directly:
```bash
cd /home/<USER>/projects
python3 memory_mcp_server.py
```

You should see:
```
🧠 Starting Memory MCP Server...
This server can store:
  - Login credentials (encrypted)
  - Step-by-step processes
  - General memories
...
Server ready for MCP connections...
```

Press Ctrl+C to stop the test.

## Usage Examples

Once connected to Claude Desktop, you can use these commands:

### Store Login Credentials
```
Please store my GitHub credentials:
- Name: github
- Username: myusername
- Password: mypassword123
- URL: https://github.com
- Notes: Personal GitHub account
```

### Store a Process
```
Please store a process for looking up parts:
- Name: lookup_parts
- Description: How to search for electronic components
- Steps:
  1. Go to Digi-Key website
  2. Use the search bar with part number
  3. Check availability and pricing
  4. Compare with Mouser if needed
  5. Add to cart or save for later
- Category: procurement
```

### Remember General Information
```
Please remember that my preferred IDE is VS Code and I like to use dark themes.
```

### Retrieve Information
```
What are my GitHub credentials?
Show me the process for looking up parts.
What do you remember about my IDE preferences?
```

### Search Memories
```
Search for anything related to "github"
Find all processes in the "procurement" category
```

## Available Tools

### Storage Tools
- `store_credential(name, username, password, url, notes)` - Store encrypted login info
- `store_process(name, description, steps, category, tags)` - Store step-by-step procedures  
- `remember(key, content, category, tags)` - Store general information

### Retrieval Tools
- `get_credential(name)` - Get specific login credentials
- `get_process(name)` - Get specific process steps
- `recall(query)` - Search all memories by keyword

### Management Tools
- `list_credentials()` - List all stored credential names
- `list_processes()` - List all stored processes
- `list_memories()` - List all general memories
- `forget_credential(name)` - Delete specific credentials
- `forget_process(name)` - Delete specific process
- `forget_memory(key)` - Delete specific memory

### Information
- Resource `memory://stats` - Get storage statistics

## Security Features

- **Encryption**: Passwords and sensitive data are encrypted using Fernet (AES 128)
- **Local Storage**: All data stored locally in `~/.fastmcp_memory/`
- **File Permissions**: Key files have restricted permissions (600)
- **Input Validation**: All inputs are validated and sanitized

## File Structure

```
~/.fastmcp_memory/
├── credentials.json    # Encrypted login credentials
├── processes.json      # Step-by-step procedures
├── memories.json       # General memories
└── .key               # Encryption key (auto-generated)
```

## Troubleshooting

### Server Won't Start
1. Check Python path in configuration
2. Ensure FastMCP is installed: `pip install fastmcp`
3. Install cryptography: `pip install cryptography`

### Claude Desktop Can't Connect
1. Verify configuration file location
2. Check absolute paths in config
3. Restart Claude Desktop after config changes

### Permission Errors
```bash
chmod 600 ~/.fastmcp_memory/.key
chmod 755 ~/.fastmcp_memory/
```

### Reset All Memory Data
```bash
rm -rf ~/.fastmcp_memory/
```
The server will recreate the directory and encryption key on next startup.

## Example Workflow

1. **Store your commonly used credentials:**
   ```
   Store my AWS credentials: username=chris, password=secret123, url=aws.amazon.com
   Store my database credentials: username=admin, password=dbpass456, url=localhost:5432
   ```

2. **Create processes for common tasks:**
   ```
   Create a process for deploying to production:
   1. Run tests locally
   2. Create pull request
   3. Get code review approval
   4. Merge to main branch
   5. Monitor deployment pipeline
   6. Verify in production
   ```

3. **Remember important information:**
   ```
   Remember that our production database backup runs at 2 AM EST daily
   Remember that the staging environment is reset every Sunday
   ```

4. **Use the memories in conversations:**
   ```
   "What are my AWS credentials?"
   "Show me the production deployment process"
   "What did I tell you about database backups?"
   ```

This creates a persistent memory system that makes Claude Desktop much more useful for your daily workflows!
