# Memory MCP Server for <PERSON> 🧠

A FastMCP-based memory server that gives Claude Desktop persistent memory capabilities for storing login credentials, processes, and general information.

## 🚀 Quick Start

1. **Install dependencies:**
   ```bash
   cd /home/<USER>/projects/fastmcp
   source fastmcp_env/bin/activate
   pip install cryptography
   ```

2. **Test the server:**
   ```bash
   cd /home/<USER>/projects
   python3 example_usage.py  # Populate with sample data
   python3 memory_mcp_server.py  # Test server startup
   ```

3. **Configure <PERSON>:**
   ```bash
   mkdir -p ~/.config/claude-desktop
   cp claude_desktop_config.json ~/.config/claude-desktop/claude_desktop_config.json
   ```

4. **Restart Claude Desktop** and start using your persistent memory!

## 🎯 What It Does

### 🔐 Secure Credential Storage
- Store usernames, passwords, API keys
- Automatic encryption using Fernet (AES 128)
- Machine-specific key generation

### 📋 Process Documentation  
- Step-by-step procedures for common tasks
- Categorized and searchable
- Perfect for "how to look up parts" type workflows

### 🧠 General Memory
- Remember preferences, schedules, server info
- Searchable by keywords
- Categorized organization

## 🛠️ Available Tools

| Tool | Purpose | Example |
|------|---------|---------|
| `store_credential` | Save login info | Store GitHub credentials |
| `get_credential` | Retrieve login info | Get my AWS password |
| `store_process` | Save procedures | Store parts lookup process |
| `get_process` | Get procedures | Show deployment steps |
| `remember` | Store general info | Remember my IDE preferences |
| `recall` | Search memories | Find anything about "server" |
| `list_*` | List stored items | Show all credentials |
| `forget_*` | Delete items | Remove old password |

## 💬 Example Conversations with Claude

**Store credentials:**
> "Please store my GitHub credentials: username=chris_dev, password=ghp_token123, url=github.com"

**Store a process:**
> "Store a process called 'lookup_parts' with these steps: 1) Go to Digi-Key, 2) Search by part number, 3) Check availability, 4) Compare prices with Mouser"

**Remember information:**
> "Remember that our production database backup runs at 2 AM EST daily"

**Retrieve information:**
> "What are my GitHub credentials?"
> "Show me the parts lookup process"
> "What do you remember about database backups?"

**Search memories:**
> "Find everything related to 'production'"

## 📁 File Structure

```
~/.fastmcp_memory/
├── credentials.json    # Encrypted login credentials  
├── processes.json      # Step-by-step procedures
├── memories.json       # General memories
└── .key               # Encryption key (auto-generated)
```

## 🔒 Security Features

- **Encryption**: Passwords encrypted with Fernet (AES 128)
- **Local Storage**: All data stays on your machine
- **Restricted Permissions**: Key file has 600 permissions
- **Input Validation**: All inputs sanitized and validated
- **Machine-Specific**: Encryption key tied to your machine

## 🧪 Testing

The `example_usage.py` script demonstrates all functionality and populates your memory with sample data:

- 3 sample credentials (GitHub, AWS, Database)
- 3 sample processes (parts lookup, deployment, troubleshooting)  
- 4 sample memories (preferences, schedules, server info)

## 🔧 Troubleshooting

**Server won't start:**
- Check Python path in Claude config
- Ensure FastMCP installed: `pip install fastmcp`
- Install cryptography: `pip install cryptography`

**Claude can't connect:**
- Verify config file location: `~/.config/claude-desktop/claude_desktop_config.json`
- Check absolute paths in config
- Restart Claude Desktop

**Reset all data:**
```bash
rm -rf ~/.fastmcp_memory/
```

## 🎓 Educational Notes

This memory server demonstrates several important concepts:

1. **MCP Server Architecture**: How to build tools, resources, and proper FastMCP integration
2. **Security Best Practices**: Encryption, input validation, file permissions
3. **Data Persistence**: JSON-based storage with proper error handling
4. **User Experience**: Clear tool descriptions and helpful error messages

The server uses a simple but effective approach:
- JSON files for easy backup/restore
- Fernet encryption for sensitive data
- Category-based organization for easy searching
- Comprehensive input validation for reliability

## 🚀 Next Steps

Once you have this working, you could extend it with:
- Database backend for better performance
- Backup/sync capabilities
- Team sharing features
- Advanced search with embeddings
- Integration with password managers

But for personal use with Claude Desktop, this JSON-based approach is perfect - simple, reliable, and secure!

---

**Created with FastMCP 2.0** - The fast, Pythonic way to build MCP servers! 🚀
