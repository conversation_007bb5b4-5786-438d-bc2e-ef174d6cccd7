#!/usr/bin/env python3
"""
Example usage of the Memory MCP Server
This demonstrates how to interact with the memory server programmatically
"""

import sys
import os
sys.path.insert(0, 'fastmcp/src')

from memory_mcp_server import MemoryManager

def main():
    print("🧠 Memory MCP Server Example Usage")
    print("=" * 50)
    
    # Initialize the memory manager
    manager = MemoryManager()
    
    # Example 1: Store some credentials
    print("\n📝 Storing Credentials...")
    
    credentials = [
        ("github", "chris_dev", "ghp_example123", "https://github.com", "Personal GitHub account"),
        ("aws_prod", "chris.admin", "aws_secret_key", "https://console.aws.amazon.com", "Production AWS account"),
        ("database", "db_admin", "super_secret_db_pass", "localhost:5432", "Main application database")
    ]
    
    for name, username, password, url, notes in credentials:
        result = manager.store_credential(name, username, password, url, notes)
        print(f"  ✅ {result}")
    
    # Example 2: Store some processes
    print("\n📋 Storing Processes...")
    
    processes = [
        ("lookup_parts", "How to search for electronic components", [
            "Go to Digi-Key website (digikey.com)",
            "Use the search bar with part number or description",
            "Filter by availability and package type",
            "Check pricing for different quantities",
            "Compare with Mouser Electronics if needed",
            "Add to cart or save to favorites for later"
        ], "procurement"),
        
        ("deploy_to_production", "Safe production deployment process", [
            "Run full test suite locally",
            "Create feature branch and push changes",
            "Open pull request with detailed description",
            "Get code review from at least 2 team members",
            "Merge to staging branch first",
            "Test in staging environment",
            "Merge to main branch",
            "Monitor deployment pipeline",
            "Verify application health in production",
            "Update deployment documentation"
        ], "devops"),
        
        ("troubleshoot_server", "Server troubleshooting checklist", [
            "Check server status and uptime",
            "Review recent logs for errors",
            "Monitor CPU and memory usage",
            "Check disk space availability",
            "Verify network connectivity",
            "Restart services if necessary",
            "Document the issue and resolution"
        ], "maintenance")
    ]
    
    for name, description, steps, category in processes:
        result = manager.store_process(name, description, steps, category)
        print(f"  ✅ {result}")
    
    # Example 3: Store some general memories
    print("\n🧠 Storing General Memories...")
    
    memories = [
        ("ide_preferences", "I prefer VS Code with dark theme, Fira Code font, and vim keybindings", "preferences"),
        ("meeting_schedule", "Team standup every Monday at 9 AM, sprint planning every other Wednesday", "schedule"),
        ("server_info", "Production server IP: *************, staging: *************", "infrastructure"),
        ("backup_schedule", "Database backups run daily at 2 AM EST, full system backup weekly on Sunday", "maintenance")
    ]
    
    for key, content, category in memories:
        result = manager.remember(key, content, category)
        print(f"  ✅ {result}")
    
    # Example 4: Demonstrate retrieval
    print("\n🔍 Retrieving Information...")
    
    # Get a credential
    github_cred = manager.get_credential("github")
    print(f"  GitHub username: {github_cred.get('username')}")
    print(f"  GitHub URL: {github_cred.get('url')}")
    
    # Get a process
    deploy_process = manager.get_process("deploy_to_production")
    print(f"  Deployment process has {len(deploy_process.get('steps', []))} steps")
    print(f"  First step: {deploy_process.get('steps', [''])[0]}")
    
    # Search memories
    search_results = manager.recall("server")
    print(f"  Found {len(search_results)} items related to 'server':")
    for item in search_results:
        print(f"    - {item.get('type')}: {item.get('name', item.get('key', 'Unknown'))}")
    
    print("\n✅ Example completed successfully!")
    print(f"\n📁 Memory files stored in: {manager.memory_dir}")
    print("\nYou can now use these memories with Claude Desktop!")

if __name__ == "__main__":
    main()
