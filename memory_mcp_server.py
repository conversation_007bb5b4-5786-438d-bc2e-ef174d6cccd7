#!/usr/bin/env python3
"""
Memory MCP Server for Claude Desktop
Stores login credentials, processes, and general memories securely
"""

import json
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any
import base64
from cryptography.fernet import Fernet
import platform
import hashlib

from fastmcp import FastMCP

# Create FastMCP server
mcp = FastMCP("Memory Server 🧠", dependencies=["cryptography"])

# Configuration
MEMORY_DIR = Path.home() / ".fastmcp_memory"
CREDENTIALS_FILE = MEMORY_DIR / "credentials.json"
PROCESSES_FILE = MEMORY_DIR / "processes.json"
MEMORIES_FILE = MEMORY_DIR / "memories.json"
CONFIG_FILE = MEMORY_DIR / "config.json"

class MemoryManager:
    def __init__(self):
        self.memory_dir = MEMORY_DIR
        self.memory_dir.mkdir(exist_ok=True)
        self._cipher_suite = None
        self._ensure_encryption_key()
    
    def _ensure_encryption_key(self):
        """Initialize encryption for sensitive data"""
        key_file = self.memory_dir / ".key"

        if not key_file.exists():
            # Generate a new key automatically
            # Use a combination of machine-specific info for key derivation
            import platform
            import hashlib

            # Create a machine-specific seed (not super secure but practical)
            machine_info = f"{platform.node()}{platform.machine()}{os.environ.get('USER', 'default')}"
            seed = hashlib.sha256(machine_info.encode()).digest()

            # Generate key from seed
            key = base64.urlsafe_b64encode(seed)

            # Store the key
            with open(key_file, 'wb') as f:
                f.write(key)

            # Set restrictive permissions
            os.chmod(key_file, 0o600)
        else:
            # Load existing key
            with open(key_file, 'rb') as f:
                key = f.read()

        self._cipher_suite = Fernet(key)
    
    def _encrypt_data(self, data: str) -> str:
        """Encrypt sensitive data"""
        return self._cipher_suite.encrypt(data.encode()).decode()
    
    def _decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        return self._cipher_suite.decrypt(encrypted_data.encode()).decode()
    
    def _load_json_file(self, file_path: Path) -> Dict:
        """Load JSON file or return empty dict"""
        if file_path.exists():
            with open(file_path, 'r') as f:
                return json.load(f)
        return {}
    
    def _save_json_file(self, file_path: Path, data: Dict):
        """Save data to JSON file"""
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2, default=str)
    
    def store_credential(self, name: str, username: str, password: str,
                        url: str = "", notes: str = "") -> str:
        """Store login credentials securely"""
        # Input validation
        if not name or not name.strip():
            return "Error: Credential name cannot be empty"
        if not username or not username.strip():
            return "Error: Username cannot be empty"
        if not password:
            return "Error: Password cannot be empty"

        credentials = self._load_json_file(CREDENTIALS_FILE)

        credential_data = {
            "username": username.strip(),
            "password": self._encrypt_data(password),
            "url": url.strip(),
            "notes": notes.strip(),
            "created": datetime.now().isoformat(),
            "updated": datetime.now().isoformat()
        }

        credentials[name.strip()] = credential_data
        self._save_json_file(CREDENTIALS_FILE, credentials)

        return f"Stored credentials for '{name.strip()}'"
    
    def get_credential(self, name: str) -> Dict:
        """Retrieve login credentials"""
        credentials = self._load_json_file(CREDENTIALS_FILE)
        
        if name not in credentials:
            return {"error": f"No credentials found for '{name}'"}
        
        cred = credentials[name].copy()
        cred["password"] = self._decrypt_data(cred["password"])
        return cred
    
    def store_process(self, name: str, description: str, steps: List[str],
                     category: str = "general", tags: List[str] = None) -> str:
        """Store a step-by-step process"""
        # Input validation
        if not name or not name.strip():
            return "Error: Process name cannot be empty"
        if not description or not description.strip():
            return "Error: Process description cannot be empty"
        if not steps or len(steps) == 0:
            return "Error: Process must have at least one step"
        if any(not step.strip() for step in steps):
            return "Error: All process steps must be non-empty"

        processes = self._load_json_file(PROCESSES_FILE)

        process_data = {
            "description": description.strip(),
            "steps": [step.strip() for step in steps],
            "category": category.strip(),
            "tags": [tag.strip() for tag in (tags or []) if tag.strip()],
            "created": datetime.now().isoformat(),
            "updated": datetime.now().isoformat()
        }

        processes[name.strip()] = process_data
        self._save_json_file(PROCESSES_FILE, processes)

        return f"Stored process '{name.strip()}' with {len(steps)} steps"
    
    def get_process(self, name: str) -> Dict:
        """Retrieve a stored process"""
        processes = self._load_json_file(PROCESSES_FILE)
        
        if name not in processes:
            return {"error": f"No process found for '{name}'"}
        
        return processes[name]
    
    def remember(self, key: str, content: str, category: str = "general", 
                tags: List[str] = None) -> str:
        """Store general memory"""
        memories = self._load_json_file(MEMORIES_FILE)
        
        memory_data = {
            "content": content,
            "category": category,
            "tags": tags or [],
            "created": datetime.now().isoformat(),
            "updated": datetime.now().isoformat()
        }
        
        memories[key] = memory_data
        self._save_json_file(MEMORIES_FILE, memories)
        
        return f"Remembered '{key}'"
    
    def recall(self, query: str) -> List[Dict]:
        """Search memories by keyword"""
        all_memories = []
        
        # Search credentials (names and usernames only, not passwords)
        credentials = self._load_json_file(CREDENTIALS_FILE)
        for name, cred in credentials.items():
            if query.lower() in name.lower() or query.lower() in cred.get("username", "").lower():
                all_memories.append({
                    "type": "credential",
                    "name": name,
                    "username": cred.get("username", ""),
                    "url": cred.get("url", ""),
                    "notes": cred.get("notes", "")
                })
        
        # Search processes
        processes = self._load_json_file(PROCESSES_FILE)
        for name, proc in processes.items():
            if (query.lower() in name.lower() or 
                query.lower() in proc.get("description", "").lower() or
                any(query.lower() in step.lower() for step in proc.get("steps", []))):
                all_memories.append({
                    "type": "process",
                    "name": name,
                    "description": proc.get("description", ""),
                    "category": proc.get("category", ""),
                    "steps_count": len(proc.get("steps", []))
                })
        
        # Search general memories
        memories = self._load_json_file(MEMORIES_FILE)
        for key, mem in memories.items():
            if (query.lower() in key.lower() or 
                query.lower() in mem.get("content", "").lower()):
                all_memories.append({
                    "type": "memory",
                    "key": key,
                    "content": mem.get("content", ""),
                    "category": mem.get("category", "")
                })
        
        return all_memories

# Initialize memory manager
memory_manager = MemoryManager()

@mcp.tool
def store_credential(name: str, username: str, password: str, 
                    url: str = "", notes: str = "") -> str:
    """Store login credentials securely.
    
    Args:
        name: Identifier for the credential (e.g., 'github', 'aws_prod')
        username: Username or email
        password: Password or API key (will be encrypted)
        url: Optional URL or endpoint
        notes: Optional notes about this credential
    """
    return memory_manager.store_credential(name, username, password, url, notes)

@mcp.tool
def get_credential(name: str) -> Dict:
    """Retrieve stored login credentials.
    
    Args:
        name: Identifier for the credential
    """
    return memory_manager.get_credential(name)

@mcp.tool
def store_process(name: str, description: str, steps: List[str], 
                 category: str = "general", tags: List[str] = None) -> str:
    """Store a step-by-step process or procedure.
    
    Args:
        name: Identifier for the process (e.g., 'lookup_parts', 'deploy_app')
        description: Brief description of what this process does
        steps: List of steps to follow
        category: Category to organize processes
        tags: Optional tags for searching
    """
    return memory_manager.store_process(name, description, steps, category, tags or [])

@mcp.tool
def get_process(name: str) -> Dict:
    """Retrieve a stored process.
    
    Args:
        name: Identifier for the process
    """
    return memory_manager.get_process(name)

@mcp.tool
def remember(key: str, content: str, category: str = "general", 
            tags: List[str] = None) -> str:
    """Store general information to remember.
    
    Args:
        key: Identifier for the memory
        content: The information to remember
        category: Category to organize memories
        tags: Optional tags for searching
    """
    return memory_manager.remember(key, content, category, tags or [])

@mcp.tool
def recall(query: str) -> List[Dict]:
    """Search stored memories by keyword.

    Args:
        query: Search term to find in stored memories
    """
    return memory_manager.recall(query)

@mcp.tool
def list_credentials() -> List[str]:
    """List all stored credential names."""
    credentials = memory_manager._load_json_file(CREDENTIALS_FILE)
    return list(credentials.keys())

@mcp.tool
def list_processes() -> List[Dict]:
    """List all stored processes with basic info."""
    processes = memory_manager._load_json_file(PROCESSES_FILE)
    return [
        {
            "name": name,
            "description": proc.get("description", ""),
            "category": proc.get("category", ""),
            "steps_count": len(proc.get("steps", []))
        }
        for name, proc in processes.items()
    ]

@mcp.tool
def list_memories() -> List[Dict]:
    """List all stored general memories."""
    memories = memory_manager._load_json_file(MEMORIES_FILE)
    return [
        {
            "key": key,
            "content": mem.get("content", "")[:100] + "..." if len(mem.get("content", "")) > 100 else mem.get("content", ""),
            "category": mem.get("category", ""),
            "created": mem.get("created", "")
        }
        for key, mem in memories.items()
    ]

@mcp.tool
def forget_credential(name: str) -> str:
    """Delete a stored credential.

    Args:
        name: Identifier for the credential to delete
    """
    credentials = memory_manager._load_json_file(CREDENTIALS_FILE)
    if name in credentials:
        del credentials[name]
        memory_manager._save_json_file(CREDENTIALS_FILE, credentials)
        return f"Deleted credential '{name}'"
    return f"No credential found for '{name}'"

@mcp.tool
def forget_process(name: str) -> str:
    """Delete a stored process.

    Args:
        name: Identifier for the process to delete
    """
    processes = memory_manager._load_json_file(PROCESSES_FILE)
    if name in processes:
        del processes[name]
        memory_manager._save_json_file(PROCESSES_FILE, processes)
        return f"Deleted process '{name}'"
    return f"No process found for '{name}'"

@mcp.tool
def forget_memory(key: str) -> str:
    """Delete a stored memory.

    Args:
        key: Identifier for the memory to delete
    """
    memories = memory_manager._load_json_file(MEMORIES_FILE)
    if key in memories:
        del memories[key]
        memory_manager._save_json_file(MEMORIES_FILE, memories)
        return f"Deleted memory '{key}'"
    return f"No memory found for '{key}'"

@mcp.resource("memory://stats")
def get_memory_stats():
    """Get statistics about stored memories."""
    credentials = memory_manager._load_json_file(CREDENTIALS_FILE)
    processes = memory_manager._load_json_file(PROCESSES_FILE)
    memories = memory_manager._load_json_file(MEMORIES_FILE)

    return {
        "credentials_count": len(credentials),
        "processes_count": len(processes),
        "memories_count": len(memories),
        "total_items": len(credentials) + len(processes) + len(memories),
        "storage_location": str(MEMORY_DIR)
    }

if __name__ == "__main__":
    print("🧠 Starting Memory MCP Server...")
    print("This server can store:")
    print("  - Login credentials (encrypted)")
    print("  - Step-by-step processes")
    print("  - General memories")
    print("\nAvailable tools:")
    print("  Storage: store_credential, store_process, remember")
    print("  Retrieval: get_credential, get_process, recall")
    print("  Listing: list_credentials, list_processes, list_memories")
    print("  Deletion: forget_credential, forget_process, forget_memory")
    print("  Resource: memory://stats")
    print(f"\nMemory storage location: {MEMORY_DIR}")
    print("Server ready for MCP connections...")
    mcp.run()
